#!/usr/bin/env python3
"""
测试JavaScript方法是否能绕过遮罩层
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter


async def test_js_approach():
    """测试JavaScript方法"""
    print("🔍 测试JavaScript方法绕过遮罩层")
    
    # 创建适配器实例（非无头模式，便于观察）
    adapter = ToutiaoPlaywrightAdapter(
        headless=False,  # 显示浏览器窗口
        timeout=60000,   # 60秒超时
        max_retries=1    # 减少重试次数
    )
    
    # 准备认证凭证
    credentials = {
        "cookies_file": "cookies/toutiao/wlchdxk_cookies.json"
    }
    
    try:
        async with adapter:
            print("🔐 开始认证...")
            auth_result = await adapter.authenticate(credentials)
            
            if not auth_result.success:
                print(f"❌ 认证失败: {auth_result.error_message}")
                return
            
            print("✅ 认证成功")
            
            # 导航到发布页面
            print("🌐 导航到发布页面...")
            await adapter._page.goto("https://mp.toutiao.com/profile_v3/graphic/publish")
            await asyncio.sleep(3)
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            await asyncio.sleep(5)
            
            # 查找标题输入框
            print("🔍 查找标题输入框...")
            title_input = adapter._page.locator('textarea[placeholder*="标题"]')
            
            if await title_input.count() > 0:
                print("✅ 找到标题输入框")
                
                # 检查是否可见和可编辑
                first_element = title_input.first
                is_visible = await first_element.is_visible()
                is_enabled = await first_element.is_enabled()
                print(f"元素状态: 可见={is_visible}, 可编辑={is_enabled}")
                
                if is_visible and is_enabled:
                    # 测试JavaScript方法
                    test_title = "JavaScript测试标题"
                    print(f"🧪 测试JavaScript方法设置标题: {test_title}")
                    
                    try:
                        # 先移除遮罩层
                        print("移除遮罩层...")
                        await adapter._page.evaluate('''() => {
                            const masks = document.querySelectorAll('.byte-drawer-mask, .ai-assistant-drawer, [class*="mask"], [class*="overlay"]');
                            masks.forEach(mask => {
                                if (mask) {
                                    mask.style.display = 'none';
                                    mask.style.visibility = 'hidden';
                                    mask.style.opacity = '0';
                                    mask.style.pointerEvents = 'none';
                                    mask.style.zIndex = '-1';
                                    try {
                                        mask.remove();
                                    } catch (e) {
                                        console.log('Failed to remove mask:', e);
                                    }
                                }
                            });
                        }''')
                        
                        await asyncio.sleep(1)
                        
                        # 设置标题值
                        print("设置标题值...")
                        await first_element.evaluate(f'element => {{ element.value = "{test_title}"; }}')
                        
                        # 触发事件
                        print("触发事件...")
                        await first_element.evaluate('''element => {
                            element.dispatchEvent(new Event("input", { bubbles: true }));
                            element.dispatchEvent(new Event("change", { bubbles: true }));
                            element.dispatchEvent(new Event("keyup", { bubbles: true }));
                        }''')
                        
                        await asyncio.sleep(1)
                        
                        # 验证结果
                        filled_value = await first_element.input_value()
                        if filled_value == test_title:
                            print(f"✅ JavaScript方法成功！标题已设置为: {filled_value}")
                        else:
                            print(f"❌ JavaScript方法失败，期望: {test_title}, 实际: {filled_value}")
                            
                    except Exception as js_error:
                        print(f"❌ JavaScript方法出错: {str(js_error)}")
                        
                    # 测试传统点击方法（对比）
                    print("🧪 测试传统点击方法...")
                    try:
                        await first_element.click(timeout=5000)
                        print("✅ 传统点击方法成功")
                    except Exception as click_error:
                        print(f"❌ 传统点击方法失败: {str(click_error)}")
                        
                else:
                    print("❌ 标题输入框不可用")
            else:
                print("❌ 未找到标题输入框")
                
            # 保持浏览器打开一段时间以便观察
            print("⏳ 保持浏览器打开30秒以便观察...")
            await asyncio.sleep(30)
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_js_approach())
