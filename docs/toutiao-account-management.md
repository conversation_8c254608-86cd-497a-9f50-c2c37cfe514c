# 今日头条账户管理和Cookie机制详解

## 📋 概述

TextUp 的今日头条适配器使用基于账户名称的 Cookie 管理系统，支持多账户并行管理。本文档详细说明账户设置、登录流程和故障排除方法。

## 🔐 账户管理机制

### 账户名称系统

**账户名称的作用：**
- 区分不同的今日头条账户
- 为每个账户单独存储 cookies
- 支持多账户并行管理
- 避免账户间的登录状态冲突

**命名建议：**
- 使用有意义的名称：`personal`、`work`、`company`
- 避免特殊字符，使用字母、数字、下划线
- 保持简洁明了，便于记忆和管理

### Cookie 存储规则

```
账户名称 → Cookie文件路径
personal → cookies/toutiao/personal_cookies.json
work → cookies/toutiao/work_cookies.json
test → cookies/toutiao/test_cookies.json
default → cookies/toutiao/default_cookies.json
```

**存储位置：**
- 默认目录：`cookies/toutiao/`
- 文件格式：`{账户名}_cookies.json`
- 包含内容：浏览器 cookies、会话信息、保存时间戳

**目录结构：**
```
cookies/
├── toutiao/           # 今日头条平台cookie
│   ├── personal_cookies.json
│   ├── work_cookies.json
│   └── test_cookies.json
├── zhihu/             # 知乎平台cookie（未来支持）
└── weibo/             # 微博平台cookie（未来支持）
```

## 🚀 账户设置和登录流程

### 方法1：手动登录（推荐）

**适用场景：**
- 首次设置账户
- 需要处理验证码
- 账户有特殊安全设置
- 最高成功率的登录方式

**操作步骤：**

1. **启动登录命令**
   ```bash
   uv run python scripts/toutiao_cli.py login --account personal --manual
   ```

2. **浏览器操作**
   - 系统自动打开浏览器
   - 导航到今日头条登录页面 (`https://mp.toutiao.com/`)
   - 手动完成登录流程：
     - 输入手机号码
     - 输入密码或短信验证码
     - 完成人机验证（滑块、图片识别等）
     - 确保成功进入创作者后台页面

3. **完成登录**
   - 登录成功后，回到终端窗口
   - 按 `Enter` 键确认完成
   - 系统自动保存 cookies 到对应文件
   - 显示保存成功的确认信息

**示例输出：**
```
=== 今日头条账户登录 ===
正在启动浏览器...
请在浏览器中完成登录，然后按 Enter 键继续...
✓ 登录成功，cookies已保存到: cookies/toutiao_personal_cookies.json
```

### 方法2：自动登录（可选）

**适用场景：**
- 账户没有复杂验证
- 批量脚本自动化
- 已知用户名密码

**操作命令：**
```bash
uv run python scripts/toutiao_cli.py login \
    --account personal \
    --username "您的手机号" \
    --password "您的密码"
```

**注意事项：**
- 可能遇到验证码问题
- 安全性相对较低
- 建议仅在测试环境使用

### 方法3：导入现有 Cookies

**适用场景：**
- 从其他工具迁移
- 备份恢复
- 团队共享（注意安全）

**操作步骤：**
```bash
# 1. 准备 cookies 文件（JSON格式）
# 2. 复制到指定位置
cp your_cookies.json cookies/toutiao_personal_cookies.json

# 3. 验证 cookies 有效性
uv run python scripts/toutiao_cli.py accounts --verify personal
```

## 🔧 账户管理操作

### 查看账户列表

```bash
# 查看所有已保存的账户
uv run python scripts/toutiao_cli.py accounts --list
```

**示例输出：**
```
=== 账户管理 ===
已保存的账户:
  - personal
  - work
  - test
```

### 验证账户状态

```bash
# 验证特定账户的 cookies 是否有效
uv run python scripts/toutiao_cli.py accounts --verify personal

# 批量验证多个账户
uv run python scripts/toutiao_cli.py accounts --verify personal
uv run python scripts/toutiao_cli.py accounts --verify work
uv run python scripts/toutiao_cli.py accounts --verify test
```

**示例输出：**
```
✓ 账户 personal 的cookies有效
✗ 账户 work 的cookies已过期，请重新登录
```

### 删除账户

```bash
# 删除指定账户的 cookies
uv run python scripts/toutiao_cli.py accounts --delete old_account

# 确认删除
ls cookies/toutiao_*_cookies.json
```

### 备份和恢复

```bash
# 备份重要账户的 cookies
mkdir -p cookies/backup
cp cookies/toutiao_personal_cookies.json cookies/backup/personal_$(date +%Y%m%d).json

# 恢复备份
cp cookies/backup/personal_20241209.json cookies/toutiao_personal_cookies.json

# 验证恢复结果
uv run python scripts/toutiao_cli.py accounts --verify personal
```

## 📝 实际使用示例

### 单账户使用

```bash
# 1. 设置账户
uv run python scripts/toutiao_cli.py login --account personal --manual

# 2. 发布文章
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --title "我的第一篇文章" \
    --content "<p>这是文章内容</p>" \
    --tags "生活,分享"

# 3. 批量发布
uv run python scripts/toutiao_cli.py batch articles.json --account personal
```

### 多账户管理

```bash
# 1. 设置多个账户
uv run python scripts/toutiao_cli.py login --account personal --manual
uv run python scripts/toutiao_cli.py login --account work --manual
uv run python scripts/toutiao_cli.py login --account brand --manual

# 2. 查看所有账户
uv run python scripts/toutiao_cli.py accounts --list

# 3. 使用不同账户发布
uv run python scripts/toutiao_cli.py publish --account personal --title "个人生活分享"
uv run python scripts/toutiao_cli.py publish --account work --title "技术文章"
uv run python scripts/toutiao_cli.py publish --account brand --title "品牌宣传"

# 4. 定期验证账户状态
for account in personal work brand; do
    echo "验证账户: $account"
    uv run python scripts/toutiao_cli.py accounts --verify $account
done
```

### 团队协作场景

```bash
# 1. 团队成员各自设置账户
# 成员A
uv run python scripts/toutiao_cli.py login --account member_a --manual

# 成员B  
uv run python scripts/toutiao_cli.py login --account member_b --manual

# 2. 共享账户（谨慎使用）
uv run python scripts/toutiao_cli.py login --account team_shared --manual

# 3. 使用指定账户发布
uv run python scripts/toutiao_cli.py publish --account member_a --file my_article.json
uv run python scripts/toutiao_cli.py publish --account team_shared --file team_article.json
```

## ⚠️ 故障排除

### 常见问题及解决方案

#### 1. 登录失败

**问题现象：**
- 浏览器无法打开
- 页面加载超时
- 登录页面异常

**解决方案：**
```bash
# 检查 Playwright 环境
playwright --version
playwright install --dry-run chromium

# 重新安装浏览器
playwright install chromium --force

# 使用调试模式
uv run python scripts/toutiao_cli.py login --account debug --manual --timeout 120

# 检查网络连接
curl -I https://mp.toutiao.com/
ping mp.toutiao.com
```

#### 2. Cookies 过期

**问题现象：**
- 发布时提示需要登录
- 验证失败
- 自动跳转到登录页

**解决方案：**
```bash
# 验证 cookies 状态
uv run python scripts/toutiao_cli.py accounts --verify personal

# 重新登录刷新 cookies
uv run python scripts/toutiao_cli.py login --account personal --manual

# 清理过期 cookies
rm cookies/toutiao_personal_cookies.json
uv run python scripts/toutiao_cli.py login --account personal --manual
```

#### 3. 发布失败

**问题现象：**
- 内容提交失败
- 页面元素找不到
- 操作超时

**解决方案：**
```bash
# 增加超时时间
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --title "测试" \
    --content "内容" \
    --timeout 120

# 使用非无头模式观察
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --title "测试" \
    --content "内容"

# 验证内容格式
uv run python scripts/toutiao_cli.py publish \
    --account personal \
    --file test.json \
    --dry-run
```

#### 4. 多账户冲突

**问题现象：**
- 账户混乱
- 登录状态错误
- 发布到错误账户

**解决方案：**
```bash
# 确认账户列表
uv run python scripts/toutiao_cli.py accounts --list

# 检查文件对应关系
ls -la cookies/toutiao_*_cookies.json

# 清理冲突账户
uv run python scripts/toutiao_cli.py accounts --delete conflicted_account

# 重新设置账户
uv run python scripts/toutiao_cli.py login --account correct_name --manual
```

## 🔒 安全注意事项

### Cookie 安全

1. **文件权限**
   ```bash
   # 设置适当的文件权限
   chmod 600 cookies/toutiao_*_cookies.json
   ```

2. **定期更新**
   - 定期重新登录刷新 cookies
   - 避免长期使用同一套 cookies
   - 监控账户异常登录

3. **备份策略**
   - 重要账户定期备份
   - 使用加密存储备份文件
   - 不要在公共环境存储 cookies

### 账户安全

1. **密码管理**
   - 使用强密码
   - 启用两步验证
   - 定期更换密码

2. **使用环境**
   - 避免在公共网络登录
   - 使用可信的设备和环境
   - 及时清理浏览器缓存

3. **权限控制**
   - 最小权限原则
   - 定期审查账户权限
   - 监控账户活动日志

## 📚 最佳实践

### 账户命名规范

```bash
# 推荐的命名方式
personal          # 个人账户
work             # 工作账户  
company_brand    # 公司品牌账户
test_dev         # 开发测试账户
backup_main      # 备用主账户

# 避免的命名方式
account1         # 无意义数字
test_account     # 过于通用
my-account       # 包含特殊字符
```

### 工作流程建议

1. **开发阶段**
   ```bash
   # 使用测试账户
   uv run python scripts/toutiao_cli.py login --account test_dev --manual
   uv run python scripts/toutiao_cli.py publish --account test_dev --dry-run
   ```

2. **生产环境**
   ```bash
   # 使用正式账户
   uv run python scripts/toutiao_cli.py login --account production --manual
   uv run python scripts/toutiao_cli.py publish --account production
   ```

3. **定期维护**
   ```bash
   # 每周验证账户状态
   for account in $(uv run python scripts/toutiao_cli.py accounts --list | grep -E "^\s*-" | sed 's/^\s*- //'); do
       echo "验证账户: $account"
       uv run python scripts/toutiao_cli.py accounts --verify $account
   done
   ```

通过遵循本文档的指导，您可以高效、安全地管理多个今日头条账户，实现稳定的自动化内容发布。
