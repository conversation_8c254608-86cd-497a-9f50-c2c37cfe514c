# TextUp 文档中心

欢迎来到 TextUp 文档中心！这里包含了使用 TextUp 所需的所有文档和指南。

## 📚 文档导航

### 🚀 快速开始

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [快速上手指南](quick-start-guide.md) | 10分钟完成安装和首次发布 | 新用户 |
| [本地测试指南](local-testing-guide.md) | 完整的本地测试和验证流程 | 开发者 |
| [部署指南](deployment-guide.md) | 生产环境部署和配置详解 | 运维人员 |

### 📱 平台专项指南

| 平台 | 快速上手 | 详细指南 | 特殊说明 |
|------|----------|----------|----------|
| **今日头条** | [5分钟快速开始](toutiao-quick-start.md) | [账户管理详解](toutiao-account-management.md) | 使用Playwright自动化 |
| **知乎** | 即将推出 | 即将推出 | 使用Playwright自动化 |
| **微博** | 即将推出 | 即将推出 | API集成 |

### 🛠️ 开发文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [开发测试指南](testing/development-testing-guide.md) | 完整的开发和测试流程 | 开发者 |
| [AI工作流](ai-automated-workflow.md) | AI自动化开发工作流程 | 开发者 |
| [API参考](api/) | 完整的API接口文档 | 开发者 |

### 📊 项目文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [最终交付报告](delivery-report.md) | 项目交付状态和技术指标 | 项目经理 |
| [配置指南](configuration.md) | 详细的配置说明 | 系统管理员 |
| [Cookie目录结构](cookie-directory-structure.md) | Cookie分平台目录管理说明 | 开发者/用户 |
| [常见问题](faq.md) | 常见问题和解决方案 | 所有用户 |

## 🎯 按使用场景选择文档

### 👋 我是新用户
1. 从 [快速上手指南](quick-start-guide.md) 开始
2. 选择您要使用的平台：
   - 今日头条：[5分钟快速开始](toutiao-quick-start.md)
   - 其他平台：查看对应的平台指南
3. 遇到问题时查看 [常见问题](faq.md)

### 🧪 我要进行测试
1. 阅读 [本地测试指南](local-testing-guide.md)
2. 查看 [开发测试指南](testing/development-testing-guide.md)
3. 针对特定平台进行测试：
   - 今日头条：参考 [账户管理详解](toutiao-account-management.md)

### 🚀 我要部署到生产环境
1. 阅读 [部署指南](deployment-guide.md)
2. 配置相关平台账户
3. 参考 [配置指南](configuration.md) 进行详细配置

### 👨‍💻 我是开发者
1. 查看 [开发测试指南](testing/development-testing-guide.md)
2. 了解 [AI工作流](ai-automated-workflow.md)
3. 参考 [API文档](api/) 进行开发
4. 查看项目的 [交付报告](delivery-report.md) 了解技术细节

## 📱 平台特殊说明

### 今日头条 (Toutiao)
- **技术方案**: 使用 Playwright 浏览器自动化
- **原因**: 今日头条没有开放文章发布的公开API
- **特点**: 需要手动登录获取cookies，支持多账户管理
- **Cookie存储**: `cookies/toutiao/` 目录下分账户存储
- **文档**:
  - [快速上手](toutiao-quick-start.md) - 5分钟开始使用
  - [详细指南](toutiao-account-management.md) - 完整的账户和cookie管理

### 知乎 (Zhihu)
- **技术方案**: 使用 Playwright 浏览器自动化
- **原因**: 知乎没有开放文章发布的公开API
- **状态**: 开发中
- **文档**: 即将推出

### 微博 (Weibo)
- **技术方案**: API集成
- **状态**: 计划中
- **文档**: 即将推出

## 🔍 文档搜索指南

### 按关键词查找

| 关键词 | 相关文档 |
|--------|----------|
| **登录、账户、cookie** | [今日头条账户管理](toutiao-account-management.md) |
| **发布、上传、文章** | [快速上手指南](quick-start-guide.md), [今日头条快速开始](toutiao-quick-start.md) |
| **测试、调试、开发** | [开发测试指南](testing/development-testing-guide.md) |
| **部署、生产、配置** | [部署指南](deployment-guide.md), [配置指南](configuration.md) |
| **错误、问题、故障** | [常见问题](faq.md), [今日头条账户管理](toutiao-account-management.md#故障排除) |

### 按文件类型查找

- **`.md` 文件**: Markdown格式的文档
- **`/api/` 目录**: API接口文档
- **`/testing/` 目录**: 测试相关文档
- **`*-guide.md`**: 各种指南文档
- **`*-quick-start.md`**: 快速上手文档

## 📝 文档贡献

如果您发现文档有问题或需要改进，欢迎：

1. **报告问题**: 在 GitHub Issues 中报告文档问题
2. **提出建议**: 在 GitHub Discussions 中提出改进建议
3. **贡献内容**: 提交 Pull Request 改进文档

### 文档编写规范

- 使用 Markdown 格式
- 包含清晰的标题和目录
- 提供实际的代码示例
- 添加适当的表情符号增强可读性
- 保持内容的准确性和时效性

## 🆘 获取帮助

如果您在使用文档过程中遇到问题：

1. **查看相关文档**: 使用上面的导航找到对应文档
2. **搜索常见问题**: 查看 [常见问题](faq.md)
3. **查看故障排除**: 各平台文档都包含故障排除章节
4. **联系支持**: 
   - GitHub Issues: 报告问题
   - GitHub Discussions: 讨论和提问
   - 邮件支持: <EMAIL>

---

**提示**: 建议将本页面加入书签，方便随时查找所需文档。
