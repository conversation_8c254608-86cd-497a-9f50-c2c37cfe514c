# Cookie目录结构说明

## 📋 概述

TextUp 使用分平台的cookie目录结构来管理不同平台的认证信息，支持多平台、多账户的独立管理。

## 🗂️ 目录结构

### 新的目录结构（推荐）

```
cookies/
├── toutiao/                    # 今日头条平台
│   ├── personal_cookies.json  # 个人账户
│   ├── work_cookies.json      # 工作账户
│   └── test_cookies.json      # 测试账户
├── zhihu/                      # 知乎平台（未来支持）
│   ├── personal_cookies.json
│   └── work_cookies.json
└── weibo/                      # 微博平台（未来支持）
    ├── personal_cookies.json
    └── company_cookies.json
```

### 旧的目录结构（已弃用）

```
cookies/
├── toutiao_personal_cookies.json
├── toutiao_work_cookies.json
├── zhihu_personal_cookies.json
└── weibo_company_cookies.json
```

## 🔄 迁移说明

### 自动迁移

TextUp 提供了自动迁移工具，可以将旧格式的cookie文件迁移到新的目录结构：

```bash
# 运行迁移脚本
python scripts/migrate_cookies.py
```

迁移过程：
1. 扫描 `cookies/` 目录下的旧格式文件
2. 解析文件名提取平台和账户信息
3. 创建对应的平台子目录
4. 将cookie数据保存到新位置
5. 删除旧格式文件

### 手动迁移

如果需要手动迁移，请按以下步骤操作：

1. **创建平台目录**：
   ```bash
   mkdir -p cookies/toutiao
   mkdir -p cookies/zhihu
   mkdir -p cookies/weibo
   ```

2. **移动文件**：
   ```bash
   # 今日头条
   mv cookies/toutiao_personal_cookies.json cookies/toutiao/personal_cookies.json
   mv cookies/toutiao_work_cookies.json cookies/toutiao/work_cookies.json
   
   # 知乎
   mv cookies/zhihu_personal_cookies.json cookies/zhihu/personal_cookies.json
   
   # 微博
   mv cookies/weibo_company_cookies.json cookies/weibo/company_cookies.json
   ```

## 🎯 优势

### 1. 清晰的组织结构
- 每个平台有独立的目录
- 避免文件名冲突
- 便于管理和维护

### 2. 扩展性强
- 新增平台时只需创建新目录
- 支持任意数量的账户
- 便于批量操作

### 3. 向后兼容
- 自动检测并迁移旧格式文件
- 迁移过程安全可靠
- 保持数据完整性

## 🛠️ 开发者说明

### Cookie管理器API

```python
from textup.utils.cookie_manager import CookieManager

# 初始化
cookie_manager = CookieManager("cookies")

# 保存cookie（新格式）
cookie_manager.save_cookies("toutiao", cookies_data, "personal")
# 文件位置: cookies/toutiao/personal_cookies.json

# 加载cookie
cookies_data = cookie_manager.load_cookies("toutiao", "personal")

# 列出账户
accounts = cookie_manager.list_accounts("toutiao")
# 返回: ["personal", "work", "test"]

# 迁移旧格式
result = cookie_manager.migrate_old_cookies()
# 返回: {"migrated": 3, "failed": 0}
```

### 文件路径规则

- **平台目录**: `cookies/{platform}/`
- **文件名**: `{account}_cookies.json`
- **完整路径**: `cookies/{platform}/{account}_cookies.json`

## 🔧 故障排除

### 迁移失败

如果迁移过程中出现问题：

1. **检查文件权限**：
   ```bash
   ls -la cookies/
   chmod 755 cookies/
   ```

2. **手动备份**：
   ```bash
   cp -r cookies/ cookies_backup/
   ```

3. **重新运行迁移**：
   ```bash
   python scripts/migrate_cookies.py
   ```

### 文件格式错误

如果cookie文件格式不正确：

1. **验证JSON格式**：
   ```bash
   python -m json.tool cookies/toutiao/personal_cookies.json
   ```

2. **重新登录获取cookie**：
   ```bash
   python scripts/toutiao_cli.py login --account personal
   ```

## 📝 注意事项

1. **备份重要数据**：迁移前建议备份整个cookies目录
2. **检查权限**：确保程序有读写cookies目录的权限
3. **平台名称**：使用小写字母作为平台目录名
4. **账户名称**：避免使用特殊字符，推荐使用字母、数字、下划线

## 🔗 相关文档

- [今日头条账户管理](toutiao-account-management.md)
- [快速上手指南](quick-start-guide.md)
- [配置指南](configuration.md)
