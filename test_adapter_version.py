#!/usr/bin/env python3
"""
测试适配器版本和方法
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
import inspect

def test_adapter_methods():
    """测试适配器方法"""
    print("🔍 检查ToutiaoPlaywrightAdapter方法")
    
    # 创建适配器实例
    adapter = ToutiaoPlaywrightAdapter()
    
    # 检查_fill_title方法的源代码
    if hasattr(adapter, '_fill_title'):
        print("✅ 找到_fill_title方法")
        
        # 获取方法源代码
        try:
            source = inspect.getsource(adapter._fill_title)
            print("📄 _fill_title方法源代码片段:")
            lines = source.split('\n')
            for i, line in enumerate(lines[:20]):  # 只显示前20行
                print(f"  {i+1:2d}: {line}")
            
            # 检查是否包含JavaScript方法
            if 'JavaScript直接设置值' in source:
                print("✅ 包含JavaScript方法")
            else:
                print("❌ 不包含JavaScript方法")
                
            if 'element.evaluate' in source:
                print("✅ 包含element.evaluate调用")
            else:
                print("❌ 不包含element.evaluate调用")
                
        except Exception as e:
            print(f"❌ 无法获取源代码: {str(e)}")
    else:
        print("❌ 未找到_fill_title方法")
    
    # 检查_close_modal_overlays方法
    if hasattr(adapter, '_close_modal_overlays'):
        print("✅ 找到_close_modal_overlays方法")
        
        try:
            source = inspect.getsource(adapter._close_modal_overlays)
            if 'JavaScript直接移除遮罩层' in source:
                print("✅ _close_modal_overlays包含JavaScript方法")
            else:
                print("❌ _close_modal_overlays不包含JavaScript方法")
        except Exception as e:
            print(f"❌ 无法获取_close_modal_overlays源代码: {str(e)}")
    else:
        print("❌ 未找到_close_modal_overlays方法")
    
    # 检查文件路径
    print(f"📁 适配器文件路径: {inspect.getfile(ToutiaoPlaywrightAdapter)}")

if __name__ == "__main__":
    test_adapter_methods()
