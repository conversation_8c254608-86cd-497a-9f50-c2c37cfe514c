#!/usr/bin/env python3
"""
今日头条手动调试工具
允许用户手动操作页面，同时记录和分析操作步骤
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

class ToutiaoManualDebugger:
    def __init__(self):
        self.browser = None
        self.page = None
        self.context = None
        self.debug_log = []
        self.screenshots_dir = Path("debug_screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        
    async def start_debug_session(self, account_name: str):
        """启动调试会话"""
        print("🚀 启动今日头条手动调试会话...")
        
        # 启动浏览器
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-sandbox',
                '--disable-dev-shm-usage'
            ]
        )
        
        # 创建上下文
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        # 加载cookies
        await self._load_cookies(account_name)
        
        # 导航到发布页面
        print("📝 导航到今日头条发布页面...")
        await self.page.goto('https://mp.toutiao.com/profile_v4/graphic/publish')
        await asyncio.sleep(3)
        
        # 等待页面加载
        await self._wait_for_page_ready()
        
        print("✅ 调试会话已启动！")
        print("\n" + "="*60)
        print("🎯 手动调试指南:")
        print("1. 浏览器窗口已打开，你可以手动操作页面")
        print("2. 在终端中输入命令来记录和分析操作")
        print("3. 可用命令:")
        print("   - 'screenshot' : 截图当前页面")
        print("   - 'analyze' : 分析当前页面元素")
        print("   - 'record' : 记录当前操作步骤")
        print("   - 'test_title' : 测试标题输入")
        print("   - 'inspect' : 检查特定元素")
        print("   - 'help' : 显示帮助")
        print("   - 'quit' : 退出调试")
        print("="*60 + "\n")
        
        # 开始交互式调试循环
        await self._interactive_debug_loop()
        
    async def _load_cookies(self, account_name: str):
        """加载cookies"""
        cookies_file = Path(f"cookies/toutiao_{account_name}.json")
        if cookies_file.exists():
            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
                await self.context.add_cookies(cookies)
                print(f"✅ 已加载账户 {account_name} 的cookies")
        else:
            print(f"⚠️  未找到账户 {account_name} 的cookies文件")
            
    async def _wait_for_page_ready(self):
        """等待页面准备就绪"""
        try:
            # 等待关键元素出现
            await self.page.wait_for_selector('textarea[placeholder*="标题"], input[placeholder*="标题"]', timeout=10000)
            print("✅ 页面已准备就绪")
        except Exception as e:
            print(f"⚠️  页面加载可能未完成: {e}")
            
    async def _interactive_debug_loop(self):
        """交互式调试循环"""
        while True:
            try:
                command = input("\n🔧 请输入调试命令 (输入 'help' 查看帮助): ").strip().lower()
                
                if command == 'quit' or command == 'q':
                    break
                elif command == 'help' or command == 'h':
                    await self._show_help()
                elif command == 'screenshot' or command == 's':
                    await self._take_screenshot()
                elif command == 'analyze' or command == 'a':
                    await self._analyze_page()
                elif command == 'record' or command == 'r':
                    await self._record_operation()
                elif command == 'test_title' or command == 't':
                    await self._test_title_input()
                elif command == 'inspect' or command == 'i':
                    await self._inspect_element()
                elif command == 'console' or command == 'c':
                    await self._show_console_logs()
                elif command == 'network' or command == 'n':
                    await self._analyze_network()
                else:
                    print(f"❌ 未知命令: {command}")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出调试...")
                break
            except Exception as e:
                print(f"❌ 命令执行错误: {e}")
                
        await self._cleanup()
        
    async def _show_help(self):
        """显示帮助信息"""
        print("\n📖 可用命令:")
        print("  screenshot (s)  - 截图当前页面状态")
        print("  analyze (a)     - 分析页面中的表单元素")
        print("  record (r)      - 记录当前操作步骤")
        print("  test_title (t)  - 测试标题输入功能")
        print("  inspect (i)     - 检查特定元素")
        print("  console (c)     - 显示浏览器控制台日志")
        print("  network (n)     - 分析网络请求")
        print("  help (h)        - 显示此帮助")
        print("  quit (q)        - 退出调试会话")
        
    async def _take_screenshot(self):
        """截图"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = self.screenshots_dir / f"manual_debug_{timestamp}.png"
        await self.page.screenshot(path=screenshot_path, full_page=True)
        print(f"📸 截图已保存: {screenshot_path}")
        
    async def _analyze_page(self):
        """分析页面元素"""
        print("🔍 分析页面中的表单元素...")
        
        # 查找所有可能的标题输入框
        title_selectors = [
            'textarea[placeholder*="标题"]',
            'input[placeholder*="标题"]',
            'input[placeholder*="请输入标题"]',
            '.title-input input',
            '.title-input textarea',
            '[data-testid*="title"]',
            '[name*="title"]',
            '[id*="title"]'
        ]
        
        found_elements = []
        for selector in title_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        value = await element.input_value() if await element.get_attribute('value') is not None else await element.text_content()
                        
                        found_elements.append({
                            'selector': selector,
                            'index': i,
                            'visible': is_visible,
                            'enabled': is_enabled,
                            'value': value,
                            'element': element
                        })
                        
                        print(f"  ✅ 找到元素: {selector}[{i}]")
                        print(f"     可见: {is_visible}, 可用: {is_enabled}")
                        print(f"     当前值: '{value}'")
                        
            except Exception as e:
                print(f"  ❌ 选择器失败: {selector} - {e}")
                
        if not found_elements:
            print("❌ 未找到任何标题输入元素")
        else:
            print(f"✅ 总共找到 {len(found_elements)} 个可能的标题输入元素")
            
        return found_elements
        
    async def _record_operation(self):
        """记录操作步骤"""
        operation = input("📝 请描述你刚才的操作: ")
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取当前页面状态
        url = self.page.url
        title = await self.page.title()
        
        log_entry = {
            'timestamp': timestamp,
            'operation': operation,
            'url': url,
            'page_title': title
        }
        
        self.debug_log.append(log_entry)
        print(f"✅ 已记录操作: {operation}")
        
        # 保存调试日志
        log_file = self.screenshots_dir / "debug_log.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.debug_log, f, ensure_ascii=False, indent=2)
            
    async def _test_title_input(self):
        """测试标题输入"""
        print("🧪 测试标题输入功能...")
        
        test_title = "测试标题 - " + datetime.now().strftime("%H:%M:%S")
        print(f"测试标题: {test_title}")
        
        # 分析页面元素
        elements = await self._analyze_page()
        
        if not elements:
            print("❌ 没有找到标题输入元素")
            return
            
        # 尝试输入到第一个可见可用的元素
        for element_info in elements:
            if element_info['visible'] and element_info['enabled']:
                try:
                    element = element_info['element']
                    print(f"🎯 尝试输入到: {element_info['selector']}[{element_info['index']}]")
                    
                    # 聚焦元素
                    await element.focus()
                    await asyncio.sleep(0.5)
                    
                    # 清空并输入
                    await element.fill('')
                    await asyncio.sleep(0.2)
                    await element.type(test_title, delay=100)
                    await asyncio.sleep(0.5)
                    
                    # 验证输入
                    new_value = await element.input_value()
                    if new_value == test_title:
                        print(f"✅ 输入成功: '{new_value}'")
                    else:
                        print(f"⚠️  输入不完整: 期望 '{test_title}', 实际 '{new_value}'")
                        
                    break
                    
                except Exception as e:
                    print(f"❌ 输入失败: {e}")
                    
    async def _inspect_element(self):
        """检查特定元素"""
        selector = input("🔍 请输入要检查的CSS选择器: ")
        
        try:
            elements = await self.page.query_selector_all(selector)
            if not elements:
                print(f"❌ 未找到匹配的元素: {selector}")
                return
                
            print(f"✅ 找到 {len(elements)} 个匹配元素:")
            
            for i, element in enumerate(elements):
                print(f"\n元素 [{i}]:")
                
                # 基本属性
                tag_name = await element.evaluate('el => el.tagName')
                is_visible = await element.is_visible()
                is_enabled = await element.is_enabled()
                
                print(f"  标签: {tag_name}")
                print(f"  可见: {is_visible}")
                print(f"  可用: {is_enabled}")
                
                # 属性
                attributes = await element.evaluate('''el => {
                    const attrs = {};
                    for (let attr of el.attributes) {
                        attrs[attr.name] = attr.value;
                    }
                    return attrs;
                }''')
                
                print(f"  属性: {json.dumps(attributes, ensure_ascii=False, indent=4)}")
                
                # 值和文本
                if tag_name.lower() in ['input', 'textarea']:
                    value = await element.input_value()
                    print(f"  值: '{value}'")
                else:
                    text = await element.text_content()
                    print(f"  文本: '{text}'")
                    
        except Exception as e:
            print(f"❌ 检查元素失败: {e}")
            
    async def _show_console_logs(self):
        """显示浏览器控制台日志"""
        print("📋 浏览器控制台日志将在页面操作时显示...")
        
        # 设置控制台监听器
        def handle_console(msg):
            print(f"🖥️  控制台 [{msg.type}]: {msg.text}")
            
        self.page.on("console", handle_console)
        print("✅ 控制台监听器已启用")
        
    async def _analyze_network(self):
        """分析网络请求"""
        print("🌐 开始监听网络请求...")
        
        requests = []
        
        def handle_request(request):
            if 'api' in request.url or 'ajax' in request.url:
                requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'timestamp': datetime.now().strftime("%H:%M:%S")
                })
                print(f"📡 API请求: {request.method} {request.url}")
                
        def handle_response(response):
            if 'api' in response.url or 'ajax' in response.url:
                print(f"📨 API响应: {response.status} {response.url}")
                
        self.page.on("request", handle_request)
        self.page.on("response", handle_response)
        
        print("✅ 网络监听器已启用，请进行操作...")
        
    async def _cleanup(self):
        """清理资源"""
        print("🧹 清理调试会话...")
        
        if self.debug_log:
            log_file = self.screenshots_dir / "final_debug_log.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.debug_log, f, ensure_ascii=False, indent=2)
            print(f"📝 调试日志已保存: {log_file}")
            
        if self.browser:
            await self.browser.close()
            
        print("✅ 调试会话已结束")

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python toutiao_manual_debug.py <账户名>")
        print("例如: python toutiao_manual_debug.py wlchdxk")
        return
        
    account_name = sys.argv[1]
    debugger = ToutiaoManualDebugger()
    
    try:
        await debugger.start_debug_session(account_name)
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 调试会话错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
