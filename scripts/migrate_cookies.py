#!/usr/bin/env python3
"""
Cookie目录结构迁移脚本

将旧格式的cookie文件迁移到新的分平台目录结构：
- 旧格式: cookies/platform_account_cookies.json
- 新格式: cookies/platform/account_cookies.json
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from textup.utils.cookie_manager import CookieManager


def main():
    """主函数"""
    print("=== Cookie目录结构迁移工具 ===")
    print()
    
    # 初始化Cookie管理器
    cookies_dir = Path(__file__).parent.parent / "cookies"
    cookie_manager = CookieManager(str(cookies_dir))
    
    print(f"Cookie目录: {cookies_dir}")
    print()
    
    # 检查是否有需要迁移的文件
    old_files = list(cookies_dir.glob("*_*_cookies.json"))
    if not old_files:
        print("✓ 没有发现需要迁移的旧格式cookie文件")
        return
    
    print(f"发现 {len(old_files)} 个旧格式cookie文件:")
    for file in old_files:
        print(f"  - {file.name}")
    print()
    
    # 确认迁移
    response = input("是否开始迁移? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("取消迁移")
        return
    
    print("开始迁移...")
    print()
    
    # 执行迁移
    result = cookie_manager.migrate_old_cookies()
    
    print("=== 迁移结果 ===")
    print(f"成功迁移: {result['migrated']} 个文件")
    print(f"迁移失败: {result['failed']} 个文件")
    
    if result['migrated'] > 0:
        print()
        print("新的目录结构:")
        show_new_structure(cookies_dir)
    
    print()
    print("迁移完成!")


def show_new_structure(cookies_dir: Path):
    """显示新的目录结构"""
    for platform_dir in sorted(cookies_dir.iterdir()):
        if platform_dir.is_dir():
            print(f"📁 {platform_dir.name}/")
            cookie_files = list(platform_dir.glob("*_cookies.json"))
            for cookie_file in sorted(cookie_files):
                account = cookie_file.stem.replace('_cookies', '')
                print(f"  📄 {account}_cookies.json")


if __name__ == "__main__":
    main()
