#!/usr/bin/env python3
"""
Cookie目录结构测试脚本

测试新的分平台cookie目录结构是否正常工作
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from textup.utils.cookie_manager import <PERSON><PERSON>Mana<PERSON>


def test_cookie_manager():
    """测试Cookie管理器的各项功能"""
    print("=== Cookie管理器功能测试 ===")
    print()
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"测试目录: {temp_dir}")
        
        # 初始化Cookie管理器
        cookie_manager = CookieManager(temp_dir)
        
        # 测试数据
        test_cookies = {
            "cookies": [
                {"name": "session_id", "value": "test123", "domain": ".example.com"},
                {"name": "user_token", "value": "token456", "domain": ".example.com"}
            ],
            "origins": [
                {
                    "origin": "https://example.com",
                    "localStorage": [
                        {"name": "user_id", "value": "12345"}
                    ]
                }
            ]
        }
        
        # 测试1: 保存cookie
        print("1. 测试保存cookie...")
        success = cookie_manager.save_cookies("toutiao", test_cookies, "test_account")
        assert success, "保存cookie失败"
        print("✓ 保存成功")
        
        # 验证文件结构
        expected_path = Path(temp_dir) / "toutiao" / "test_account_cookies.json"
        assert expected_path.exists(), f"Cookie文件不存在: {expected_path}"
        print(f"✓ 文件创建成功: {expected_path}")
        
        # 测试2: 加载cookie
        print("\n2. 测试加载cookie...")
        loaded_cookies = cookie_manager.load_cookies("toutiao", "test_account")
        assert loaded_cookies is not None, "加载cookie失败"
        assert "cookies" in loaded_cookies, "Cookie数据格式错误"
        print("✓ 加载成功")
        
        # 测试3: 列出账户
        print("\n3. 测试列出账户...")
        accounts = cookie_manager.list_accounts("toutiao")
        assert "test_account" in accounts, "账户列表中没有找到测试账户"
        print(f"✓ 账户列表: {accounts}")
        
        # 测试4: 保存多个账户
        print("\n4. 测试多账户管理...")
        cookie_manager.save_cookies("toutiao", test_cookies, "account1")
        cookie_manager.save_cookies("toutiao", test_cookies, "account2")
        cookie_manager.save_cookies("zhihu", test_cookies, "zhihu_account")
        
        toutiao_accounts = cookie_manager.list_accounts("toutiao")
        zhihu_accounts = cookie_manager.list_accounts("zhihu")
        
        assert len(toutiao_accounts) >= 3, f"今日头条账户数量不正确: {toutiao_accounts}"
        assert len(zhihu_accounts) >= 1, f"知乎账户数量不正确: {zhihu_accounts}"
        print(f"✓ 今日头条账户: {toutiao_accounts}")
        print(f"✓ 知乎账户: {zhihu_accounts}")
        
        # 测试5: 删除cookie
        print("\n5. 测试删除cookie...")
        success = cookie_manager.delete_cookies("toutiao", "account1")
        assert success, "删除cookie失败"
        
        updated_accounts = cookie_manager.list_accounts("toutiao")
        assert "account1" not in updated_accounts, "账户删除失败"
        print("✓ 删除成功")
        
        # 测试6: 验证目录结构
        print("\n6. 验证目录结构...")
        temp_path = Path(temp_dir)
        
        # 检查平台目录
        toutiao_dir = temp_path / "toutiao"
        zhihu_dir = temp_path / "zhihu"
        
        assert toutiao_dir.exists() and toutiao_dir.is_dir(), "今日头条目录不存在"
        assert zhihu_dir.exists() and zhihu_dir.is_dir(), "知乎目录不存在"
        
        # 检查文件
        toutiao_files = list(toutiao_dir.glob("*_cookies.json"))
        zhihu_files = list(zhihu_dir.glob("*_cookies.json"))
        
        print(f"✓ 今日头条目录: {toutiao_dir}")
        print(f"  - 文件数量: {len(toutiao_files)}")
        for f in toutiao_files:
            print(f"  - {f.name}")
            
        print(f"✓ 知乎目录: {zhihu_dir}")
        print(f"  - 文件数量: {len(zhihu_files)}")
        for f in zhihu_files:
            print(f"  - {f.name}")
        
        print("\n✅ 所有测试通过！")


def test_migration():
    """测试迁移功能"""
    print("\n=== 迁移功能测试 ===")
    print()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"测试目录: {temp_dir}")
        temp_path = Path(temp_dir)
        
        # 创建旧格式的cookie文件
        old_files = [
            "toutiao_personal_cookies.json",
            "toutiao_work_cookies.json",
            "zhihu_test_cookies.json"
        ]
        
        test_data = {"cookies": [], "test": "data"}
        
        for filename in old_files:
            file_path = temp_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(test_data, f)
            print(f"创建旧格式文件: {filename}")
        
        # 初始化Cookie管理器并执行迁移
        cookie_manager = CookieManager(str(temp_path))
        result = cookie_manager.migrate_old_cookies()
        
        print(f"\n迁移结果: {result}")
        
        # 验证迁移结果
        assert result["migrated"] == 3, f"迁移数量不正确: {result}"
        assert result["failed"] == 0, f"迁移失败数量不为0: {result}"
        
        # 验证新文件结构
        expected_files = [
            temp_path / "toutiao" / "personal_cookies.json",
            temp_path / "toutiao" / "work_cookies.json",
            temp_path / "zhihu" / "test_cookies.json"
        ]
        
        for expected_file in expected_files:
            assert expected_file.exists(), f"迁移后文件不存在: {expected_file}"
            print(f"✓ 迁移成功: {expected_file}")
        
        # 验证旧文件已删除
        for old_file in old_files:
            old_path = temp_path / old_file
            assert not old_path.exists(), f"旧文件未删除: {old_path}"
        
        print("\n✅ 迁移测试通过！")


def test_real_environment():
    """测试真实环境"""
    print("\n=== 真实环境测试 ===")
    print()
    
    # 使用项目的cookies目录
    project_root = Path(__file__).parent.parent
    cookies_dir = project_root / "cookies"
    
    print(f"项目cookies目录: {cookies_dir}")
    
    if not cookies_dir.exists():
        print("❌ cookies目录不存在")
        return
    
    # 初始化Cookie管理器
    cookie_manager = CookieManager(str(cookies_dir))
    
    # 检查目录结构
    print("\n当前目录结构:")
    for item in sorted(cookies_dir.iterdir()):
        if item.is_dir():
            print(f"📁 {item.name}/")
            cookie_files = list(item.glob("*_cookies.json"))
            for cookie_file in sorted(cookie_files):
                account = cookie_file.stem.replace('_cookies', '')
                print(f"  📄 {account}_cookies.json")
        elif item.name.endswith('_cookies.json'):
            print(f"📄 {item.name} (旧格式)")
    
    # 测试各平台的账户列表
    platforms = ["toutiao", "zhihu", "weibo"]
    for platform in platforms:
        accounts = cookie_manager.list_accounts(platform)
        if accounts:
            print(f"\n{platform} 平台账户: {accounts}")
        else:
            print(f"\n{platform} 平台: 无账户")
    
    print("\n✅ 真实环境测试完成！")


def main():
    """主函数"""
    try:
        test_cookie_manager()
        test_migration()
        test_real_environment()
        
        print("\n🎉 所有测试通过！新的cookie目录结构工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
