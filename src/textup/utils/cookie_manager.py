"""
Cookie管理工具

本模块提供Cookie的保存、加载、验证等功能，用于Playwright适配器的认证管理。
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List

from ..utils import handle_exception


class CookieManager:
    """Cookie管理器"""

    def __init__(self, cookies_dir: str = "cookies"):
        """
        初始化Cookie管理器

        Args:
            cookies_dir: Cookie文件存储目录
        """
        self.cookies_dir = Path(cookies_dir)
        self.cookies_dir.mkdir(exist_ok=True)

    def get_cookie_file_path(self, platform: str, account: str = "default") -> str:
        """
        获取Cookie文件路径

        Args:
            platform: 平台名称
            account: 账户名称

        Returns:
            Cookie文件路径
        """
        # 创建平台子目录
        platform_dir = self.cookies_dir / platform
        platform_dir.mkdir(exist_ok=True)

        filename = f"{account}_cookies.json"
        return str(platform_dir / filename)

    def save_cookies(self, platform: str, cookies_data: Dict[str, Any], account: str = "default") -> bool:
        """
        保存Cookie数据

        Args:
            platform: 平台名称
            cookies_data: Cookie数据
            account: 账户名称

        Returns:
            是否保存成功
        """
        try:
            file_path = self.get_cookie_file_path(platform, account)
            
            # 添加保存时间戳
            cookies_data["_saved_at"] = datetime.now().isoformat()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cookies_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            handle_exception("save_cookies", e, {
                "platform": platform,
                "account": account,
                "file_path": file_path
            })
            return False

    def load_cookies(self, platform: str, account: str = "default") -> Optional[Dict[str, Any]]:
        """
        加载Cookie数据

        Args:
            platform: 平台名称
            account: 账户名称

        Returns:
            Cookie数据，如果不存在或无效则返回None
        """
        try:
            file_path = self.get_cookie_file_path(platform, account)
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            # 检查Cookie是否过期
            if self._is_cookies_expired(cookies_data):
                return None
            
            return cookies_data
            
        except Exception as e:
            handle_exception("load_cookies", e, {
                "platform": platform,
                "account": account,
                "file_path": file_path
            })
            return None

    def _is_cookies_expired(self, cookies_data: Dict[str, Any], max_age_days: int = 30) -> bool:
        """
        检查Cookie是否过期

        Args:
            cookies_data: Cookie数据
            max_age_days: 最大有效天数

        Returns:
            是否过期
        """
        try:
            saved_at_str = cookies_data.get("_saved_at")
            if not saved_at_str:
                return True
            
            saved_at = datetime.fromisoformat(saved_at_str)
            expiry_date = saved_at + timedelta(days=max_age_days)
            
            return datetime.now() > expiry_date
            
        except Exception:
            return True

    def delete_cookies(self, platform: str, account: str = "default") -> bool:
        """
        删除Cookie文件

        Args:
            platform: 平台名称
            account: 账户名称

        Returns:
            是否删除成功
        """
        try:
            file_path = self.get_cookie_file_path(platform, account)
            
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return True
            
        except Exception as e:
            handle_exception("delete_cookies", e, {
                "platform": platform,
                "account": account,
                "file_path": file_path
            })
            return False

    def list_accounts(self, platform: str) -> List[str]:
        """
        列出平台的所有账户

        Args:
            platform: 平台名称

        Returns:
            账户名称列表
        """
        try:
            accounts = []
            platform_dir = self.cookies_dir / platform

            if not platform_dir.exists():
                return accounts

            pattern = "*_cookies.json"

            for file_path in platform_dir.glob(pattern):
                filename = file_path.stem
                # 提取账户名称: account_cookies -> account
                if filename.endswith('_cookies'):
                    account = filename[:-8]  # 去掉'_cookies'后缀
                    accounts.append(account)

            return accounts

        except Exception as e:
            handle_exception("list_accounts", e, {"platform": platform})
            return []

    def validate_cookies_format(self, cookies_data: Dict[str, Any]) -> bool:
        """
        验证Cookie数据格式

        Args:
            cookies_data: Cookie数据

        Returns:
            格式是否有效
        """
        try:
            # 检查是否包含必要的字段
            required_fields = ["cookies", "origins"]
            for field in required_fields:
                if field not in cookies_data:
                    return False
            
            # 检查cookies格式
            cookies = cookies_data.get("cookies", [])
            if not isinstance(cookies, list):
                return False
            
            for cookie in cookies:
                if not isinstance(cookie, dict):
                    return False
                
                # 检查必要的cookie字段
                required_cookie_fields = ["name", "value", "domain"]
                for field in required_cookie_fields:
                    if field not in cookie:
                        return False
            
            return True
            
        except Exception:
            return False

    def clean_expired_cookies(self, max_age_days: int = 30) -> int:
        """
        清理过期的Cookie文件

        Args:
            max_age_days: 最大有效天数

        Returns:
            清理的文件数量
        """
        try:
            cleaned_count = 0

            # 遍历所有平台目录
            for platform_dir in self.cookies_dir.iterdir():
                if not platform_dir.is_dir():
                    continue

                # 遍历平台目录下的cookie文件
                for file_path in platform_dir.glob("*_cookies.json"):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            cookies_data = json.load(f)

                        if self._is_cookies_expired(cookies_data, max_age_days):
                            os.remove(file_path)
                            cleaned_count += 1

                    except Exception:
                        # 如果文件损坏，也删除它
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except Exception:
                            pass

            return cleaned_count

        except Exception as e:
            handle_exception("clean_expired_cookies", e, {"max_age_days": max_age_days})
            return 0

    def backup_cookies(self, platform: str, account: str = "default") -> Optional[str]:
        """
        备份Cookie文件

        Args:
            platform: 平台名称
            account: 账户名称

        Returns:
            备份文件路径，失败返回None
        """
        try:
            source_path = self.get_cookie_file_path(platform, account)
            
            if not os.path.exists(source_path):
                return None
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{platform}_{account}_cookies_backup_{timestamp}.json"
            backup_path = str(self.cookies_dir / backup_filename)
            
            # 复制文件
            with open(source_path, 'r', encoding='utf-8') as src:
                cookies_data = json.load(src)
            
            with open(backup_path, 'w', encoding='utf-8') as dst:
                json.dump(cookies_data, dst, ensure_ascii=False, indent=2)
            
            return backup_path
            
        except Exception as e:
            handle_exception("backup_cookies", e, {
                "platform": platform,
                "account": account
            })
            return None

    def restore_cookies(self, backup_path: str, platform: str, account: str = "default") -> bool:
        """
        从备份恢复Cookie文件

        Args:
            backup_path: 备份文件路径
            platform: 平台名称
            account: 账户名称

        Returns:
            是否恢复成功
        """
        try:
            if not os.path.exists(backup_path):
                return False
            
            # 验证备份文件格式
            with open(backup_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if not self.validate_cookies_format(cookies_data):
                return False
            
            # 恢复到目标位置
            target_path = self.get_cookie_file_path(platform, account)
            
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(cookies_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            handle_exception("restore_cookies", e, {
                "backup_path": backup_path,
                "platform": platform,
                "account": account
            })
            return False

    def migrate_old_cookies(self) -> Dict[str, int]:
        """
        将旧格式的cookie文件迁移到新的目录结构

        旧格式: cookies/platform_account_cookies.json
        新格式: cookies/platform/account_cookies.json

        Returns:
            迁移统计信息 {"migrated": 数量, "failed": 数量}
        """
        try:
            migrated = 0
            failed = 0

            # 查找旧格式的cookie文件
            old_pattern = "*_*_cookies.json"
            for old_file in self.cookies_dir.glob(old_pattern):
                try:
                    filename = old_file.stem
                    parts = filename.split('_')

                    # 解析文件名: platform_account_cookies
                    if len(parts) >= 3 and parts[-1] == 'cookies':
                        platform = parts[0]
                        account = '_'.join(parts[1:-1])

                        # 读取旧文件内容
                        with open(old_file, 'r', encoding='utf-8') as f:
                            cookies_data = json.load(f)

                        # 保存到新位置
                        if self.save_cookies(platform, cookies_data, account):
                            # 删除旧文件
                            os.remove(old_file)
                            migrated += 1
                            print(f"已迁移: {platform}/{account}")
                        else:
                            failed += 1
                            print(f"迁移失败: {old_file}")
                    else:
                        print(f"跳过不符合格式的文件: {old_file}")

                except Exception as e:
                    failed += 1
                    print(f"迁移文件 {old_file} 时出错: {str(e)}")

            return {"migrated": migrated, "failed": failed}

        except Exception as e:
            handle_exception("migrate_old_cookies", e)
            return {"migrated": 0, "failed": 0}
