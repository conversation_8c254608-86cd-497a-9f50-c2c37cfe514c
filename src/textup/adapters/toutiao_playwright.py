"""
今日头条Playwright适配器

本模块实现了基于Playwright的今日头条平台内容发布功能：
- 浏览器自动化登录
- 文章发布自动化
- 图片上传处理
- 内容格式验证

由于今日头条API不支持文章发布，本适配器使用Playwright模拟浏览器操作。
"""

import asyncio
import json
import os
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

from .playwright_base import PlaywrightBaseAdapter
from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
)
from ..utils import TextUpError, PlatformAPIError, InvalidCredentialsError, handle_exception
from ..utils.cookie_manager import <PERSON>ieManager
from ..utils.toutiao_login_helper import ToutiaoLoginHelper


def retry_on_failure(max_retries: int = 3, delay_sequence: List[float] = None):
    """重试装饰器"""
    if delay_sequence is None:
        delay_sequence = [1, 2, 4]

    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(self, *args, **kwargs)
                except Exception as e:
                    last_exception = e

                    if attempt == max_retries:
                        # 最后一次尝试失败
                        handle_exception(f"{func.__name__}_final_attempt", e, {
                            "attempt": attempt + 1,
                            "max_retries": max_retries
                        })
                        raise e

                    # 计算延迟时间
                    delay = delay_sequence[min(attempt, len(delay_sequence) - 1)]

                    print(f"{func.__name__} 第{attempt + 1}次尝试失败，{delay}秒后重试: {str(e)}")
                    await asyncio.sleep(delay)

            # 理论上不会到达这里
            raise last_exception

        return wrapper
    return decorator


class ToutiaoPlaywrightAdapter(PlaywrightBaseAdapter):
    """今日头条Playwright适配器
    
    使用Playwright自动化浏览器操作来发布文章到今日头条平台。
    支持自动登录、文章发布、图片上传等功能。
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._upload_timeout = 60000  # 上传超时时间（毫秒）
        self._retry_delays = [1, 2, 4, 8]  # 重试延迟序列（秒）
        self._max_publish_retries = 3  # 发布最大重试次数

    @property
    def platform(self) -> Platform:
        """返回平台标识"""
        return Platform.TOUTIAO

    @property
    def login_url(self) -> str:
        """返回登录页面URL"""
        return "https://mp.toutiao.com/"

    @property
    def publish_url(self) -> str:
        """返回发布页面URL"""
        return "https://mp.toutiao.com/profile_v3/graphic/publish"

    @property
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        return ["cookies_file"]  # 主要依赖cookies文件

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证"""
        errors = []
        
        # 检查cookies文件路径
        cookies_file = credentials.get("cookies_file")
        if not cookies_file:
            errors.append(ValidationError(
                field="cookies_file",
                message="cookies文件路径不能为空",
                value=cookies_file
            ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors
        )

    async def _is_logged_in(self) -> bool:
        """检查是否已登录"""
        try:
            # 检查是否存在登录相关的元素
            login_indicators = [
                'text="手机号登录"',
                'text="扫码登录"',
                'text="登录"',
                '.login-container',
                '.qr-code-container'
            ]
            
            for indicator in login_indicators:
                if await self._page.locator(indicator).count() > 0:
                    return False
            
            # 检查是否存在用户信息或发布相关元素
            user_indicators = [
                '.user-info',
                '.avatar',
                'text="发布"',
                'text="草稿"',
                '.publish-btn'
            ]
            
            for indicator in user_indicators:
                if await self._page.locator(indicator).count() > 0:
                    return True
                    
            return False
            
        except Exception as e:
            handle_exception("check_login_status", e)
            return False

    async def _perform_login(self, credentials: Dict[str, Any]) -> bool:
        """执行登录操作"""
        try:
            # 导航到登录页面
            await self._page.goto(self.login_url, wait_until="networkidle")
            
            # 如果提供了用户名和密码，尝试自动登录
            username = credentials.get("username")
            password = credentials.get("password")
            
            if username and password:
                return await self._auto_login(username, password)
            else:
                # 手动登录模式
                return await self._manual_login()
                
        except Exception as e:
            handle_exception("perform_login", e, credentials)
            return False

    async def _auto_login(self, username: str, password: str) -> bool:
        """自动登录"""
        try:
            # 点击手机号登录
            phone_login_btn = self._page.locator('text="手机号登录"')
            if await phone_login_btn.count() > 0:
                await phone_login_btn.click()
                await asyncio.sleep(1)
            
            # 输入用户名
            username_input = self._page.locator('input[placeholder*="手机号"], input[type="tel"]')
            if await username_input.count() > 0:
                await username_input.fill(username)
                await asyncio.sleep(0.5)
            
            # 输入密码
            password_input = self._page.locator('input[type="password"]')
            if await password_input.count() > 0:
                await password_input.fill(password)
                await asyncio.sleep(0.5)
            
            # 点击登录按钮
            login_btn = self._page.locator('button:has-text("登录"), .login-btn')
            if await login_btn.count() > 0:
                await login_btn.click()
                
                # 等待登录完成
                await self._page.wait_for_url("**/profile**", timeout=30000)
                return True
            
            return False
            
        except Exception as e:
            handle_exception("auto_login", e)
            return False

    async def _manual_login(self) -> bool:
        """手动登录模式"""
        try:
            print("请在浏览器中完成登录操作...")
            print("登录完成后，程序将自动继续...")
            
            # 等待用户手动登录
            await self._page.pause()
            
            # 检查是否登录成功
            return await self._is_logged_in()
            
        except Exception as e:
            handle_exception("manual_login", e)
            return False

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """验证内容格式"""
        errors = []
        
        # 检查标题
        if not content.title or len(content.title.strip()) == 0:
            errors.append(ValidationError(
                field="title",
                message="标题不能为空",
                value=content.title
            ))
        elif len(content.title) > 100:
            errors.append(ValidationError(
                field="title",
                message="标题长度不能超过100个字符",
                value=len(content.title)
            ))
        
        # 检查内容
        if not content.content or len(content.content.strip()) == 0:
            errors.append(ValidationError(
                field="content",
                message="内容不能为空",
                value=content.content
            ))
        elif len(content.content) > 50000:
            errors.append(ValidationError(
                field="content",
                message="内容长度不能超过50000个字符",
                value=len(content.content)
            ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors
        )

    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """具体的发布实现"""
        for attempt in range(self._max_publish_retries + 1):
            try:
                # 导航到发布页面
                await self._navigate_to_publish_page()

                # 等待页面加载完成
                await self._wait_for_page_ready()

                # 填写标题
                await self._fill_title_with_retry(content.title)

                # 填写内容
                await self._fill_content_with_retry(content.content)

                # 处理封面图片
                if hasattr(content, 'cover_image') and content.cover_image:
                    await self._upload_cover_image_with_retry(content.cover_image)

                # 处理发布选项
                await self._set_publish_options_with_retry(options)

                # 发布文章
                post_id = await self._submit_article_with_retry()

                return PublishResult(
                    success=True,
                    platform=self.platform,
                    platform_post_id=post_id,
                    message="文章发布成功",
                    published_url=f"https://mp.toutiao.com/profile_v3/graphic/publish?pgc_id={post_id}" if post_id else None
                )

            except Exception as e:
                if attempt == self._max_publish_retries:
                    # 最后一次尝试失败
                    return PublishResult(
                        success=False,
                        platform=self.platform,
                        error_message=f"发布失败（已重试{self._max_publish_retries}次）: {str(e)}",
                        error_details={
                            "exception": type(e).__name__,
                            "attempt": attempt + 1,
                            "max_retries": self._max_publish_retries
                        }
                    )

                # 重试前的延迟
                delay = self._retry_delays[min(attempt, len(self._retry_delays) - 1)]
                print(f"发布第{attempt + 1}次尝试失败，{delay}秒后重试: {str(e)}")
                await asyncio.sleep(delay)

                # 重新初始化页面
                try:
                    await self._page.reload(wait_until="networkidle")
                except Exception:
                    # 如果重新加载失败，尝试重新导航
                    await self._page.goto(self.publish_url, wait_until="networkidle")

    async def _navigate_to_publish_page(self):
        """导航到发布页面"""
        try:
            await self._page.goto(self.publish_url, wait_until="networkidle", timeout=30000)
        except PlaywrightTimeoutError:
            # 如果超时，尝试重新加载
            await self._page.reload(wait_until="domcontentloaded")

    async def _wait_for_page_ready(self):
        """等待页面准备就绪"""
        try:
            print("等待页面加载完成...")

            # 等待网络空闲
            await self._page.wait_for_load_state("networkidle", timeout=30000)

            # 关闭可能的弹窗或抽屉
            await self._close_modal_overlays()

            # 扩展的关键元素选择器
            key_selectors = [
                # 标题输入框
                'input[placeholder*="标题"]',
                'input[placeholder*="请输入标题"]',
                'textarea[placeholder*="标题"]',
                '.title-input input',

                # 内容编辑器
                '.ql-editor',
                '.editor-content',
                'div[contenteditable="true"]',

                # 今日头条特定元素
                '.byte-input',
                '.semi-input',

                # 发布页面标识
                'button:has-text("发布")',
                'button:has-text("发表")',
            ]

            print(f"检查关键页面元素，共 {len(key_selectors)} 个选择器")
            found_elements = []

            for i, selector in enumerate(key_selectors):
                try:
                    print(f"检查选择器 {i+1}/{len(key_selectors)}: {selector}")
                    await self._page.wait_for_selector(selector, timeout=5000)
                    count = await self._page.locator(selector).count()
                    print(f"  找到 {count} 个匹配元素")
                    found_elements.append(selector)
                except PlaywrightTimeoutError:
                    print(f"  选择器超时: {selector}")
                    continue
                except Exception as e:
                    print(f"  选择器错误: {selector} - {str(e)}")
                    continue

            if found_elements:
                print(f"✓ 页面准备就绪，找到 {len(found_elements)} 个关键元素")
            else:
                print("⚠ 未找到预期的关键元素，但继续执行")
                # 保存调试信息
                await self._save_debug_info("page_not_ready")

            # 额外等待时间确保页面完全加载
            print("等待页面稳定...")
            await asyncio.sleep(3)

        except Exception as e:
            print(f"等待页面准备时出错: {str(e)}")
            await self._save_debug_screenshot("page_ready_error")
            handle_exception("wait_for_page_ready", e)

    async def _close_modal_overlays(self):
        """关闭可能的弹窗或抽屉覆盖层"""
        try:
            print("🔧 检查并关闭可能的弹窗...")

            # 使用更强力的JavaScript方法移除所有阻塞层
            try:
                print("🚀 使用强力JavaScript移除所有阻塞层...")
                removed_count = await self._page.evaluate('''() => {
                    console.log("开始移除阻塞层...");

                    // 更全面的选择器列表
                    const blockingSelectors = [
                        '.byte-drawer-mask',
                        '.ai-assistant-drawer',
                        '.byte-drawer-wrapper',
                        '.modal-mask',
                        '.overlay-mask',
                        '[class*="mask"]',
                        '[class*="overlay"]',
                        '[class*="drawer"]',
                        '[class*="modal"]',
                        '[class*="popup"]',
                        '[style*="pointer-events"]'
                    ];

                    let removedCount = 0;

                    // 移除所有匹配的元素
                    blockingSelectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            console.log(`找到 ${elements.length} 个 ${selector} 元素`);

                            elements.forEach(el => {
                                if (el) {
                                    // 强制隐藏和禁用
                                    el.style.display = 'none !important';
                                    el.style.visibility = 'hidden !important';
                                    el.style.opacity = '0 !important';
                                    el.style.pointerEvents = 'none !important';
                                    el.style.zIndex = '-9999 !important';
                                    el.style.position = 'absolute !important';
                                    el.style.left = '-9999px !important';
                                    el.style.top = '-9999px !important';

                                    // 移除元素
                                    try {
                                        el.remove();
                                        removedCount++;
                                        console.log(`移除了元素: ${selector}`);
                                    } catch (e) {
                                        console.log(`移除失败: ${selector}`, e);
                                    }
                                }
                            });
                        } catch (e) {
                            console.log(`处理选择器失败: ${selector}`, e);
                        }
                    });

                    // 额外处理：移除所有具有高z-index的元素
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(el => {
                        const zIndex = window.getComputedStyle(el).zIndex;
                        if (zIndex && parseInt(zIndex) > 1000) {
                            const className = el.className || '';
                            if (className.includes('mask') || className.includes('overlay') ||
                                className.includes('drawer') || className.includes('modal')) {
                                el.style.display = 'none !important';
                                el.style.pointerEvents = 'none !important';
                                try {
                                    el.remove();
                                    removedCount++;
                                    console.log(`移除了高z-index元素: ${className}`);
                                } catch (e) {}
                            }
                        }
                    });

                    console.log(`总共移除了 ${removedCount} 个阻塞元素`);
                    return removedCount;
                }''')

                print(f"✅ JavaScript移除完成，移除了 {removed_count} 个阻塞元素")
                await asyncio.sleep(1)

            except Exception as js_error:
                print(f"❌ JavaScript移除遮罩层失败: {str(js_error)}")
                import traceback
                traceback.print_exc()

            # 方法2: 按ESC键关闭弹窗
            try:
                await self._page.keyboard.press("Escape")
                await asyncio.sleep(0.5)
                print("已按ESC键尝试关闭弹窗")
            except Exception as e:
                print(f"按ESC键失败: {str(e)}")

            # 方法3: 查找并点击关闭按钮
            close_selectors = [
                # AI助手抽屉关闭按钮
                '.byte-drawer-close',
                '.ai-assistant-drawer .byte-drawer-close',
                '.byte-drawer-header .byte-drawer-close',
                'button[aria-label="关闭"]',
                'button[aria-label="Close"]',

                # 通用关闭按钮
                '.modal-close',
                '.dialog-close',
                '.popup-close',
                'button:has-text("关闭")',
                'button:has-text("取消")',
                'button:has-text("×")',

                # 今日头条特定的关闭按钮
                '.semi-modal-close',
                '.semi-drawer-close'
            ]

            for selector in close_selectors:
                try:
                    elements = await self._page.locator(selector).all()
                    for element in elements:
                        if await element.is_visible():
                            print(f"找到可见的关闭按钮: {selector}")
                            try:
                                await element.click(timeout=2000)
                                print(f"已点击关闭按钮: {selector}")
                                await asyncio.sleep(0.5)
                            except Exception as click_error:
                                print(f"点击关闭按钮失败: {str(click_error)}")
                except Exception as e:
                    print(f"尝试关闭 {selector} 时出错: {str(e)}")
                    continue

            # 方法4: 最后再按一次ESC键
            try:
                await self._page.keyboard.press("Escape")
                await asyncio.sleep(0.5)
                print("再次按ESC键尝试关闭弹窗")
            except Exception as e:
                print(f"再次按ESC键失败: {str(e)}")

        except Exception as e:
            print(f"关闭弹窗时出错: {str(e)}")

    @retry_on_failure(max_retries=2, delay_sequence=[1, 2])
    async def _fill_title_with_retry(self, title: str):
        """带重试的填写标题"""
        return await self._fill_title(title)

    @retry_on_failure(max_retries=2, delay_sequence=[1, 2])
    async def _fill_content_with_retry(self, content: str):
        """带重试的填写内容"""
        return await self._fill_content(content)

    @retry_on_failure(max_retries=2, delay_sequence=[2, 4])
    async def _upload_cover_image_with_retry(self, image_path: str):
        """带重试的上传封面图片"""
        return await self._upload_cover_image(image_path)

    @retry_on_failure(max_retries=1, delay_sequence=[1])
    async def _set_publish_options_with_retry(self, options: Dict[str, Any]):
        """带重试的设置发布选项"""
        return await self._set_publish_options(options)

    @retry_on_failure(max_retries=2, delay_sequence=[2, 4])
    async def _submit_article_with_retry(self) -> Optional[str]:
        """带重试的提交文章"""
        return await self._submit_article()

    async def _fill_title(self, title: str):
        """填写文章标题"""
        try:
            # 保存页面截图用于调试
            await self._save_debug_screenshot("before_title_fill")

            # 扩展的标题输入框选择器列表（基于实际调试结果）
            title_selectors = [
                # 实际发现的工作选择器（优先级最高）
                'textarea[placeholder*="标题"]',

                # 常见的标题输入框选择器
                'input[placeholder*="标题"]',
                'input[placeholder*="请输入标题"]',
                'input[placeholder*="输入标题"]',
                'input[placeholder*="title"]',
                'input[placeholder*="Title"]',

                # 基于类名的选择器
                '.title-input input',
                '.title-input textarea',
                '.article-title input',
                '.article-title textarea',
                '.post-title input',
                '.editor-title input',
                '.publish-title input',

                # 基于属性的选择器
                'input[name="title"]',
                'textarea[name="title"]',
                'input[data-testid*="title"]',
                'input[aria-label*="标题"]',
                'textarea[aria-label*="标题"]',

                # 通用输入框选择器（作为最后的尝试）
                'input[type="text"]:first-of-type',
                'textarea:first-of-type',

                # 今日头条可能的特定选择器
                '.byte-input input',
                '.byte-input textarea',
                '.semi-input input',
                '.semi-input textarea',
                'div[contenteditable="true"]'
            ]

            print(f"尝试查找标题输入框，共有 {len(title_selectors)} 个选择器")

            for i, selector in enumerate(title_selectors):
                try:
                    print(f"尝试选择器 {i+1}/{len(title_selectors)}: {selector}")
                    title_input = self._page.locator(selector)
                    count = await title_input.count()
                    print(f"  找到 {count} 个匹配元素")

                    if count > 0:
                        # 检查元素是否可见和可编辑
                        first_element = title_input.first
                        is_visible = await first_element.is_visible()
                        is_enabled = await first_element.is_enabled()

                        print(f"  元素可见: {is_visible}, 可编辑: {is_enabled}")

                        if is_visible and is_enabled:
                            # 先尝试关闭可能的覆盖层
                            await self._close_modal_overlays()

                            # 方法1: 尝试使用JavaScript直接设置值（绕过UI阻塞）
                            try:
                                print(f"  🔧 方法1: 使用JavaScript直接设置值: {selector}")

                                # 转义标题中的特殊字符
                                escaped_title = title.replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')

                                # 使用更强力的JavaScript方法，包括React/Vue状态更新
                                js_result = await first_element.evaluate(f'''element => {{
                                    console.log("开始设置标题:", "{escaped_title}");

                                    // 先移除所有可能的阻塞层
                                    const masks = document.querySelectorAll('.byte-drawer-mask, [class*="mask"], [class*="overlay"]');
                                    masks.forEach(mask => {{
                                        if (mask) {{
                                            mask.style.display = 'none';
                                            mask.style.visibility = 'hidden';
                                            mask.style.pointerEvents = 'none';
                                            mask.style.zIndex = '-9999';
                                            try {{ mask.remove(); }} catch(e) {{}}
                                        }}
                                    }});

                                    // 设置元素值
                                    element.value = "{escaped_title}";
                                    element.textContent = "{escaped_title}";
                                    element.innerText = "{escaped_title}";

                                    // 尝试更新React/Vue组件状态
                                    try {{
                                        // 查找React Fiber节点
                                        const reactKey = Object.keys(element).find(key => key.startsWith('__reactInternalInstance') || key.startsWith('_reactInternalFiber'));
                                        if (reactKey) {{
                                            console.log("找到React节点，尝试更新状态");
                                            const reactInstance = element[reactKey];
                                            if (reactInstance && reactInstance.memoizedProps) {{
                                                reactInstance.memoizedProps.value = "{escaped_title}";
                                                if (reactInstance.memoizedProps.onChange) {{
                                                    reactInstance.memoizedProps.onChange({{ target: {{ value: "{escaped_title}" }} }});
                                                }}
                                            }}
                                        }}

                                        // 查找Vue实例
                                        if (element.__vue__) {{
                                            console.log("找到Vue实例，尝试更新状态");
                                            element.__vue__.$emit('input', "{escaped_title}");
                                        }}
                                    }} catch(e) {{
                                        console.log("更新组件状态失败:", e);
                                    }}

                                    // 触发所有可能的事件（包括React合成事件）
                                    const events = [
                                        'input', 'change', 'keyup', 'keydown', 'blur', 'focus',
                                        'compositionstart', 'compositionend', 'textInput'
                                    ];

                                    events.forEach(eventType => {{
                                        try {{
                                            // 原生事件
                                            const nativeEvent = new Event(eventType, {{ bubbles: true, cancelable: true }});
                                            element.dispatchEvent(nativeEvent);

                                            // React合成事件
                                            if (eventType === 'input' || eventType === 'change') {{
                                                const syntheticEvent = new InputEvent(eventType, {{
                                                    bubbles: true,
                                                    cancelable: true,
                                                    data: "{escaped_title}",
                                                    inputType: 'insertText'
                                                }});
                                                element.dispatchEvent(syntheticEvent);
                                            }}
                                        }} catch(e) {{
                                            console.log("事件触发失败:", eventType, e);
                                        }}
                                    }});

                                    // 强制触发表单验证
                                    try {{
                                        if (element.form) {{
                                            element.form.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                            element.form.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                        }}
                                    }} catch(e) {{
                                        console.log("表单事件触发失败:", e);
                                    }}

                                    console.log("设置完成，当前值:", element.value);
                                    return element.value;
                                }}''')

                                print(f"  JavaScript执行结果: {js_result}")

                                # 给应用程序更多时间处理状态变化
                                print("  等待应用程序处理状态变化...")
                                await asyncio.sleep(2)

                                # 验证设置是否成功
                                filled_value = await first_element.input_value()
                                print(f"  验证结果 - 期望: '{title}', 实际: '{filled_value}'")

                                if filled_value == title:
                                    print(f"✅ 成功使用JavaScript设置标题: {selector}")

                                    # 额外等待确保状态完全同步
                                    print("  等待状态完全同步...")
                                    await asyncio.sleep(1)

                                    await self._save_debug_screenshot("after_title_fill_success")
                                    return
                                else:
                                    print(f"  ❌ JavaScript设置验证失败")

                            except Exception as js_error:
                                print(f"  ❌ JavaScript设置失败: {str(js_error)}")
                                import traceback
                                traceback.print_exc()

                            # 方法2: 尝试使用键盘输入（绕过点击）
                            try:
                                print(f"  方法2: 使用键盘输入: {selector}")

                                # 先聚焦到元素
                                await first_element.focus()
                                await asyncio.sleep(0.3)

                                # 清空现有内容
                                await first_element.press("Control+a")
                                await asyncio.sleep(0.1)
                                await first_element.press("Delete")
                                await asyncio.sleep(0.2)

                                # 输入标题
                                await first_element.type(title)
                                await asyncio.sleep(0.5)

                                # 验证输入是否成功
                                filled_value = await first_element.input_value()
                                if filled_value == title:
                                    print(f"✓ 成功使用键盘输入标题: {selector}")
                                    await self._save_debug_screenshot("after_title_fill_success")
                                    return
                                else:
                                    print(f"  键盘输入验证失败，期望: {title}, 实际: {filled_value}")

                            except Exception as keyboard_error:
                                print(f"  键盘输入失败: {str(keyboard_error)}")

                            # 方法3: 尝试传统的点击和填写方法
                            try:
                                print(f"  方法3: 传统点击方法: {selector}")
                                await first_element.click(timeout=5000)
                                await asyncio.sleep(0.5)

                                # 清空现有内容
                                await first_element.fill("")
                                await asyncio.sleep(0.2)

                                # 填写标题
                                await first_element.fill(title)
                                await asyncio.sleep(0.5)

                                # 验证填写是否成功
                                filled_value = await first_element.input_value()
                                if filled_value == title:
                                    print(f"✓ 成功使用传统方法填写标题: {selector}")
                                    await self._save_debug_screenshot("after_title_fill_success")
                                    return
                                else:
                                    print(f"  传统方法填写验证失败，期望: {title}, 实际: {filled_value}")

                            except Exception as click_error:
                                print(f"  传统点击方法失败: {str(click_error)}")

                                # 方法4: 最后尝试强制点击
                                try:
                                    print(f"  方法4: 强制点击: {selector}")
                                    await first_element.click(force=True)
                                    await asyncio.sleep(0.5)

                                    await first_element.fill("")
                                    await first_element.fill(title)
                                    await asyncio.sleep(0.5)

                                    filled_value = await first_element.input_value()
                                    if filled_value == title:
                                        print(f"✓ 成功使用强制点击填写标题: {selector}")
                                        await self._save_debug_screenshot("after_title_fill_success")
                                        return
                                    else:
                                        print(f"  强制点击填写验证失败，期望: {title}, 实际: {filled_value}")

                                except Exception as force_error:
                                    print(f"  强制点击也失败: {str(force_error)}")
                                    continue

                except Exception as selector_error:
                    print(f"  选择器 {selector} 出错: {str(selector_error)}")
                    continue

            # 如果所有选择器都失败，保存页面信息用于调试
            await self._save_debug_info("title_input_not_found")
            raise Exception("未找到标题输入框")

        except Exception as e:
            await self._save_debug_screenshot("title_fill_error")
            raise Exception(f"填写标题失败: {str(e)}")

    async def _save_debug_screenshot(self, name: str):
        """保存调试截图"""
        try:
            import os
            screenshot_dir = "screenshots"
            os.makedirs(screenshot_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"{screenshot_dir}/{name}_{timestamp}.png"

            await self._page.screenshot(path=screenshot_path, full_page=True)
            print(f"调试截图已保存: {screenshot_path}")

        except Exception as e:
            print(f"保存截图失败: {str(e)}")

    async def _save_debug_info(self, name: str):
        """保存调试信息"""
        try:
            import os
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存页面HTML
            html_content = await self._page.content()
            html_path = f"{debug_dir}/{name}_{timestamp}.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"页面HTML已保存: {html_path}")

            # 保存所有输入框信息
            input_elements = await self._page.locator('input, textarea, div[contenteditable="true"]').all()
            input_info = []

            for i, element in enumerate(input_elements):
                try:
                    tag_name = await element.evaluate('el => el.tagName')
                    placeholder = await element.get_attribute('placeholder') or ""
                    name = await element.get_attribute('name') or ""
                    class_name = await element.get_attribute('class') or ""
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()

                    input_info.append({
                        'index': i,
                        'tag': tag_name,
                        'placeholder': placeholder,
                        'name': name,
                        'class': class_name,
                        'visible': is_visible,
                        'enabled': is_enabled
                    })
                except Exception:
                    continue

            # 保存输入框信息到文件
            import json
            info_path = f"{debug_dir}/{name}_inputs_{timestamp}.json"
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(input_info, f, ensure_ascii=False, indent=2)
            print(f"输入框信息已保存: {info_path}")

        except Exception as e:
            print(f"保存调试信息失败: {str(e)}")

    async def _fill_content(self, content: str):
        """填写文章内容"""
        try:
            # 预处理内容
            processed_content = await self._preprocess_content(content)

            # 查找内容编辑器
            content_selectors = [
                '.ql-editor',
                '.editor-content',
                '.content-editor',
                'div[contenteditable="true"]',
                'textarea[placeholder*="内容"]',
                '.ProseMirror'
            ]

            for selector in content_selectors:
                content_editor = self._page.locator(selector)
                if await content_editor.count() > 0:
                    await content_editor.click()
                    await asyncio.sleep(0.5)

                    # 清空现有内容
                    await self._page.keyboard.press("Control+A")
                    await self._page.keyboard.press("Delete")
                    await asyncio.sleep(0.3)

                    # 分段输入内容（避免内容过长导致问题）
                    await self._input_content_by_chunks(content_editor, processed_content)
                    await asyncio.sleep(1)
                    return

            raise Exception("未找到内容编辑器")

        except Exception as e:
            raise Exception(f"填写内容失败: {str(e)}")

    async def _preprocess_content(self, content: str) -> str:
        """预处理内容"""
        try:
            # 移除不支持的HTML标签
            import re

            # 移除script和style标签
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 移除iframe标签
            content = re.sub(r'<iframe[^>]*>.*?</iframe>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 处理链接（今日头条可能不支持外链）
            content = re.sub(r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>', r'\2', content)

            # 处理图片标签，提取图片信息用于后续上传
            # 这里先保留img标签，后续在上传时处理

            return content.strip()

        except Exception as e:
            handle_exception("preprocess_content", e)
            return content

    async def _input_content_by_chunks(self, editor, content: str, chunk_size: int = 1000):
        """分块输入内容"""
        try:
            # 如果内容较短，直接输入
            if len(content) <= chunk_size:
                await editor.fill(content)
                return

            # 分块输入
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]

                if i == 0:
                    await editor.fill(chunk)
                else:
                    # 追加内容
                    await editor.focus()
                    await self._page.keyboard.press("Control+End")  # 移动到末尾
                    await self._page.keyboard.type(chunk)

                await asyncio.sleep(0.2)  # 短暂延迟

        except Exception as e:
            # 如果分块输入失败，尝试直接输入
            await editor.fill(content)

    async def _upload_cover_image(self, image_path: str):
        """上传封面图片"""
        try:
            if not os.path.exists(image_path):
                raise Exception(f"封面图片文件不存在: {image_path}")

            # 验证图片格式和大小
            if not await self._validate_image(image_path):
                raise Exception("图片格式或大小不符合要求")

            # 查找封面上传按钮
            cover_selectors = [
                'text="选择封面"',
                'text="设置封面"',
                '.cover-upload',
                '.upload-cover',
                'button:has-text("封面")'
            ]

            for selector in cover_selectors:
                cover_btn = self._page.locator(selector)
                if await cover_btn.count() > 0:
                    await cover_btn.click()
                    await asyncio.sleep(1)

                    # 等待上传对话框出现
                    await self._page.wait_for_selector('.upload-modal, .cover-modal, .semi-modal', timeout=5000)

                    # 查找文件上传输入框
                    file_input_selectors = [
                        'input[type="file"][accept*="image"]',
                        '.upload-input input[type="file"]',
                        'input[type="file"]'
                    ]

                    for input_selector in file_input_selectors:
                        file_input = self._page.locator(input_selector)
                        if await file_input.count() > 0:
                            await file_input.set_input_files(image_path)

                            # 等待上传完成
                            await self._wait_for_upload_complete()

                            # 查找确认按钮
                            confirm_selectors = [
                                'button:has-text("确定")',
                                'button:has-text("完成")',
                                'button:has-text("保存")',
                                '.confirm-btn'
                            ]

                            for confirm_selector in confirm_selectors:
                                confirm_btn = self._page.locator(confirm_selector)
                                if await confirm_btn.count() > 0:
                                    await confirm_btn.click()
                                    await asyncio.sleep(1)
                                    break

                            return

                    break

            print("警告: 未找到封面上传功能，跳过封面设置")

        except Exception as e:
            print(f"警告: 上传封面图片失败: {str(e)}")

    async def _validate_image(self, image_path: str) -> bool:
        """验证图片格式和大小"""
        try:
            # 检查文件扩展名
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_ext = Path(image_path).suffix.lower()

            if file_ext not in valid_extensions:
                print(f"不支持的图片格式: {file_ext}")
                return False

            # 检查文件大小（限制为10MB）
            file_size = os.path.getsize(image_path)
            max_size = 10 * 1024 * 1024  # 10MB

            if file_size > max_size:
                print(f"图片文件过大: {file_size / 1024 / 1024:.2f}MB > 10MB")
                return False

            return True

        except Exception as e:
            handle_exception("validate_image", e, {"image_path": image_path})
            return False

    async def _wait_for_upload_complete(self, timeout: int = 30):
        """等待上传完成"""
        try:
            # 等待上传进度条消失或成功提示出现
            success_indicators = [
                '.upload-success',
                'text="上传成功"',
                '.success-icon'
            ]

            progress_indicators = [
                '.upload-progress',
                '.progress-bar',
                'text="上传中"'
            ]

            # 等待上传开始
            await asyncio.sleep(1)

            # 等待上传完成
            for i in range(timeout):
                # 检查是否上传成功
                for indicator in success_indicators:
                    if await self._page.locator(indicator).count() > 0:
                        return True

                # 检查是否还在上传中
                still_uploading = False
                for indicator in progress_indicators:
                    if await self._page.locator(indicator).count() > 0:
                        still_uploading = True
                        break

                if not still_uploading:
                    # 没有进度指示器，可能已完成
                    await asyncio.sleep(1)
                    return True

                await asyncio.sleep(1)

            print("警告: 上传超时")
            return False

        except Exception as e:
            handle_exception("wait_for_upload_complete", e)
            return False

    async def _upload_content_images(self, content: str) -> str:
        """上传内容中的图片"""
        try:
            import re

            # 查找所有img标签
            img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
            img_matches = re.finditer(img_pattern, content)

            updated_content = content

            for match in img_matches:
                img_tag = match.group(0)
                img_src = match.group(1)

                # 如果是本地文件路径，上传图片
                if os.path.exists(img_src):
                    uploaded_url = await self._upload_single_image(img_src)
                    if uploaded_url:
                        # 替换图片URL
                        new_img_tag = img_tag.replace(img_src, uploaded_url)
                        updated_content = updated_content.replace(img_tag, new_img_tag)

            return updated_content

        except Exception as e:
            handle_exception("upload_content_images", e)
            return content

    async def _upload_single_image(self, image_path: str) -> Optional[str]:
        """上传单个图片并返回URL"""
        try:
            if not await self._validate_image(image_path):
                return None

            # 在编辑器中插入图片
            # 这需要根据今日头条编辑器的具体实现来调整

            # 查找图片上传按钮
            image_upload_selectors = [
                '.image-upload-btn',
                'button[title*="图片"]',
                '.toolbar .image-btn',
                'text="插入图片"'
            ]

            for selector in image_upload_selectors:
                upload_btn = self._page.locator(selector)
                if await upload_btn.count() > 0:
                    await upload_btn.click()
                    await asyncio.sleep(1)

                    # 上传文件
                    file_input = self._page.locator('input[type="file"][accept*="image"]')
                    if await file_input.count() > 0:
                        await file_input.set_input_files(image_path)

                        # 等待上传完成并获取URL
                        await self._wait_for_upload_complete()

                        # 这里需要根据实际情况获取上传后的图片URL
                        # 暂时返回原路径，实际使用时需要调整
                        return image_path

            return None

        except Exception as e:
            handle_exception("upload_single_image", e, {"image_path": image_path})
            return None

    async def _set_publish_options(self, options: Dict[str, Any]):
        """设置发布选项"""
        try:
            # 设置发布时间
            publish_time = options.get("publish_time")
            if publish_time:
                await self._set_publish_time(publish_time)
            
            # 设置标签
            tags = options.get("tags", [])
            if tags:
                await self._set_tags(tags)
            
            # 其他选项...
            
        except Exception as e:
            print(f"警告: 设置发布选项失败: {str(e)}")

    async def _set_publish_time(self, publish_time: datetime):
        """设置发布时间"""
        try:
            # 查找定时发布选项
            schedule_selectors = [
                'text="定时发布"',
                '.schedule-publish',
                'input[type="radio"][value="schedule"]'
            ]
            
            for selector in schedule_selectors:
                schedule_option = self._page.locator(selector)
                if await schedule_option.count() > 0:
                    await schedule_option.click()
                    await asyncio.sleep(1)
                    
                    # 设置时间
                    time_str = publish_time.strftime("%Y-%m-%d %H:%M")
                    time_input = self._page.locator('input[placeholder*="时间"], .time-picker input')
                    if await time_input.count() > 0:
                        await time_input.fill(time_str)
                        await self._page.keyboard.press("Enter")
                    
                    return
            
        except Exception as e:
            print(f"警告: 设置发布时间失败: {str(e)}")

    async def _set_tags(self, tags: List[str]):
        """设置文章标签"""
        try:
            # 查找标签输入框
            tag_selectors = [
                '.tag-input',
                'input[placeholder*="标签"]',
                '.tags-container input'
            ]
            
            for selector in tag_selectors:
                tag_input = self._page.locator(selector)
                if await tag_input.count() > 0:
                    for tag in tags[:5]:  # 限制标签数量
                        await tag_input.fill(f"#{tag}")
                        await self._page.keyboard.press("Space")
                        await asyncio.sleep(0.5)
                    return
            
        except Exception as e:
            print(f"警告: 设置标签失败: {str(e)}")

    async def _submit_article(self) -> Optional[str]:
        """提交发布文章"""
        try:
            # 发布前检查
            await self._pre_publish_check()

            # 查找发布按钮
            publish_btn = await self._find_publish_button()
            if not publish_btn:
                raise Exception("未找到发布按钮")

            # 检查按钮是否可点击
            if not await publish_btn.is_enabled():
                raise Exception("发布按钮不可点击，请检查内容是否完整")

            # 点击发布
            await publish_btn.click()

            # 等待发布结果
            return await self._wait_for_publish_result()

        except Exception as e:
            raise Exception(f"提交文章失败: {str(e)}")

    async def _pre_publish_check(self):
        """发布前检查"""
        try:
            # 检查标题是否已填写
            title_selectors = [
                'input[placeholder*="标题"]',
                '.title-input input',
                'input[name="title"]'
            ]

            title_filled = False
            for selector in title_selectors:
                title_input = self._page.locator(selector)
                if await title_input.count() > 0:
                    title_value = await title_input.input_value()
                    if title_value and title_value.strip():
                        title_filled = True
                        break

            if not title_filled:
                raise Exception("标题未填写")

            # 检查内容是否已填写
            content_selectors = [
                '.ql-editor',
                '.editor-content',
                'div[contenteditable="true"]'
            ]

            content_filled = False
            for selector in content_selectors:
                content_editor = self._page.locator(selector)
                if await content_editor.count() > 0:
                    content_text = await content_editor.text_content()
                    if content_text and content_text.strip():
                        content_filled = True
                        break

            if not content_filled:
                raise Exception("内容未填写")

        except Exception as e:
            raise Exception(f"发布前检查失败: {str(e)}")

    async def _find_publish_button(self):
        """查找发布按钮"""
        publish_selectors = [
            'button:has-text("发布")',
            'button:has-text("立即发布")',
            '.publish-btn',
            'button[type="submit"]',
            '.submit-btn',
            'button.primary:has-text("发布")'
        ]

        for selector in publish_selectors:
            publish_btn = self._page.locator(selector)
            if await publish_btn.count() > 0:
                return publish_btn

        return None

    async def _wait_for_publish_result(self, timeout: int = 30) -> Optional[str]:
        """等待发布结果"""
        try:
            # 等待页面跳转或成功提示
            success_indicators = [
                "**/manage**",
                "**/success**",
                "**/published**"
            ]

            # 尝试等待URL变化
            for indicator in success_indicators:
                try:
                    await self._page.wait_for_url(indicator, timeout=timeout * 1000)

                    # 尝试从URL中提取文章ID
                    current_url = self._page.url
                    match = re.search(r'pgc_id=(\d+)', current_url)
                    if match:
                        return match.group(1)

                    return "published"

                except PlaywrightTimeoutError:
                    continue

            # 如果URL没有变化，检查页面上的成功提示
            success_message_selectors = [
                'text="发布成功"',
                'text="发表成功"',
                '.success-message',
                '.publish-success'
            ]

            for selector in success_message_selectors:
                if await self._page.locator(selector).count() > 0:
                    return "published"

            # 检查是否有错误提示
            await self._check_publish_errors()

            # 如果没有明确的成功或失败提示，可能需要更长时间
            print("等待发布完成...")
            await asyncio.sleep(5)

            # 再次检查
            if await self._page.locator('text="发布成功"').count() > 0:
                return "published"

            return "published"  # 假设发布成功

        except Exception as e:
            raise Exception(f"等待发布结果失败: {str(e)}")

    async def _check_publish_errors(self):
        """检查发布错误"""
        error_selectors = [
            '.error-message',
            '.alert-error',
            '.publish-error',
            'text="发布失败"',
            'text="发表失败"',
            'text="网络错误"',
            'text="系统错误"'
        ]

        for selector in error_selectors:
            error_element = self._page.locator(selector)
            if await error_element.count() > 0:
                error_text = await error_element.text_content()
                raise Exception(f"发布错误: {error_text}")

    async def _handle_publish_error(self, error_message: str) -> bool:
        """处理发布错误"""
        try:
            # 根据错误类型采取不同的处理策略
            if "网络" in error_message or "超时" in error_message:
                print("网络错误，等待后重试...")
                await asyncio.sleep(3)
                return True  # 可以重试

            elif "标题" in error_message:
                print("标题相关错误，需要检查标题内容")
                return False  # 不建议重试

            elif "内容" in error_message:
                print("内容相关错误，需要检查内容格式")
                return False  # 不建议重试

            elif "权限" in error_message or "认证" in error_message:
                print("权限错误，需要重新登录")
                return False  # 不建议重试

            else:
                print(f"未知错误: {error_message}")
                return True  # 可以尝试重试

        except Exception as e:
            handle_exception("handle_publish_error", e, {"error_message": error_message})
            return False

    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """获取发布状态"""
        try:
            # 导航到文章管理页面
            manage_url = "https://mp.toutiao.com/profile_v3/graphic/manage"
            await self._page.goto(manage_url, wait_until="networkidle")
            
            # 查找对应的文章
            # 这里需要根据实际页面结构来实现
            
            return {
                "status": "published",
                "platform_post_id": platform_post_id,
                "message": "文章已发布"
            }
            
        except Exception as e:
            return {
                "status": "unknown",
                "platform_post_id": platform_post_id,
                "error": str(e)
            }
