#!/usr/bin/env python3
"""
今日头条选择器调试脚本
用于调试和发现正确的页面元素选择器
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from textup.adapters.toutiao_playwright import ToutiaoPlaywrightAdapter
from textup.models import TransformedContent, Platform


async def debug_page_selectors():
    """调试页面选择器"""
    print("🔍 开始调试今日头条页面选择器")
    
    # 创建适配器实例（非无头模式，便于观察）
    adapter = ToutiaoPlaywrightAdapter(
        headless=False,  # 显示浏览器窗口
        timeout=60000,   # 60秒超时
        max_retries=1    # 减少重试次数
    )
    
    # 准备认证凭证
    credentials = {
        "cookies_file": "cookies/toutiao/wlchdxk_cookies.json"
    }
    
    try:
        async with adapter:
            print("🔐 开始认证...")
            auth_result = await adapter.authenticate(credentials)
            
            if not auth_result.success:
                print(f"❌ 认证失败: {auth_result.error_message}")
                return
            
            print("✅ 认证成功")
            
            # 导航到发布页面
            print("🌐 导航到发布页面...")
            await adapter._navigate_to_publish_page()
            
            # 等待页面准备就绪
            print("⏳ 等待页面加载...")
            await adapter._wait_for_page_ready()
            
            # 保存页面截图
            print("📸 保存页面截图...")
            os.makedirs("debug", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"debug/toutiao_publish_page_{timestamp}.png"
            await adapter._page.screenshot(path=screenshot_path, full_page=True)
            print(f"截图已保存: {screenshot_path}")
            
            # 获取页面HTML
            print("📄 保存页面HTML...")
            html_content = await adapter._page.content()
            html_path = f"debug/toutiao_publish_page_{timestamp}.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"HTML已保存: {html_path}")
            
            # 查找所有输入框
            print("🔍 分析页面输入框...")
            input_elements = await adapter._page.locator('input, textarea, div[contenteditable="true"]').all()
            
            input_info = []
            for i, element in enumerate(input_elements):
                try:
                    tag_name = await element.evaluate('el => el.tagName')
                    placeholder = await element.get_attribute('placeholder') or ""
                    name = await element.get_attribute('name') or ""
                    class_name = await element.get_attribute('class') or ""
                    id_attr = await element.get_attribute('id') or ""
                    type_attr = await element.get_attribute('type') or ""
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    # 获取元素的位置信息
                    try:
                        bounding_box = await element.bounding_box()
                    except:
                        bounding_box = None
                    
                    element_info = {
                        'index': i,
                        'tag': tag_name,
                        'placeholder': placeholder,
                        'name': name,
                        'class': class_name,
                        'id': id_attr,
                        'type': type_attr,
                        'visible': is_visible,
                        'enabled': is_enabled,
                        'bounding_box': bounding_box
                    }
                    
                    input_info.append(element_info)
                    
                    # 打印元素信息
                    print(f"  [{i}] {tag_name}")
                    if placeholder:
                        print(f"      placeholder: {placeholder}")
                    if name:
                        print(f"      name: {name}")
                    if class_name:
                        print(f"      class: {class_name}")
                    if id_attr:
                        print(f"      id: {id_attr}")
                    if type_attr:
                        print(f"      type: {type_attr}")
                    print(f"      visible: {is_visible}, enabled: {is_enabled}")
                    if bounding_box:
                        print(f"      position: x={bounding_box['x']}, y={bounding_box['y']}")
                    print()
                    
                except Exception as e:
                    print(f"  [{i}] 获取元素信息失败: {str(e)}")
            
            # 保存输入框信息到JSON文件
            info_path = f"debug/toutiao_inputs_{timestamp}.json"
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(input_info, f, ensure_ascii=False, indent=2)
            print(f"输入框信息已保存: {info_path}")
            
            # 尝试查找可能的标题输入框
            print("🎯 查找可能的标题输入框...")
            potential_title_selectors = [
                'input[placeholder*="标题"]',
                'input[placeholder*="请输入标题"]',
                'input[placeholder*="输入标题"]',
                'input[placeholder*="title"]',
                'input[placeholder*="Title"]',
                '.title-input input',
                '.article-title input',
                '.post-title input',
                '.editor-title input',
                'input[name="title"]',
                'input[type="text"]:first-of-type',
                'textarea[placeholder*="标题"]',
                '.byte-input input',
                '.semi-input input',
                'div[contenteditable="true"]'
            ]
            
            found_selectors = []
            for selector in potential_title_selectors:
                try:
                    elements = await adapter._page.locator(selector).all()
                    if elements:
                        for j, elem in enumerate(elements):
                            is_visible = await elem.is_visible()
                            is_enabled = await elem.is_enabled()
                            if is_visible and is_enabled:
                                found_selectors.append({
                                    'selector': selector,
                                    'index': j,
                                    'visible': is_visible,
                                    'enabled': is_enabled
                                })
                                print(f"  ✅ 找到可用选择器: {selector} (第{j}个)")
                except Exception as e:
                    continue
            
            if found_selectors:
                print(f"\n🎉 找到 {len(found_selectors)} 个可能的标题输入框选择器")
                
                # 尝试使用第一个找到的选择器填写标题
                first_selector = found_selectors[0]
                print(f"🧪 测试选择器: {first_selector['selector']}")
                
                try:
                    test_element = adapter._page.locator(first_selector['selector']).nth(first_selector['index'])
                    await test_element.click()
                    await asyncio.sleep(0.5)
                    await test_element.fill("测试标题")
                    await asyncio.sleep(0.5)
                    
                    # 验证填写结果
                    filled_value = await test_element.input_value()
                    if filled_value == "测试标题":
                        print("✅ 标题填写测试成功！")
                        
                        # 保存成功截图
                        success_screenshot = f"debug/title_fill_success_{timestamp}.png"
                        await adapter._page.screenshot(path=success_screenshot, full_page=True)
                        print(f"成功截图已保存: {success_screenshot}")
                    else:
                        print(f"❌ 标题填写验证失败，期望: 测试标题, 实际: {filled_value}")
                        
                except Exception as e:
                    print(f"❌ 测试选择器失败: {str(e)}")
            else:
                print("❌ 未找到可用的标题输入框选择器")
            
            # 暂停以便手动检查
            print("\n⏸️  浏览器将保持打开状态30秒，请手动检查页面...")
            print("   可以手动尝试填写标题来确认正确的元素")
            await asyncio.sleep(30)
            
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(debug_page_selectors())
